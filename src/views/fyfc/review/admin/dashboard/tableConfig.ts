import { h } from 'vue';
import { NTag, NButton, NIcon, NSpace, type DataTableColumns } from 'naive-ui';
import { Eye as ViewIcon, Checkmark as CompleteIcon, Create as EditIcon, Trash as DeleteIcon } from '@vicons/ionicons5';
import type { EvaluationStatus } from '../../../../../fconfig/fyfc/review';
import { getStatusLabel } from '../../../../../fconfig/fyfc/review';
// import { fyfcOssService } from '../../../../../utils/FyfcOssService'; // 暂时注释

// 表格行数据类型
export interface EvaluationTableRow {
    id?: number;
    department?: string;
    name?: string;
    reviewDate?: number;
    additionalScore?: number;
    score?: number;
    comment?: string;
    attachments?: string; // 新增：附件JSON字符串
    status?: string;
    createdAt?: number;
    createdBy?: string;
    updatedBy?: string;
    scores?: any[];
    employeeScore?: number;
    colleagueScore?: number;
    managerScore?: number;
    managerEvaluator?: string;  // 新增：上级评分人姓名
    totalScore?: number;
    // attachmentCount?: number; // 新增：附件数量 - 暂时注释
}

// 计算所有列的总宽度
export const calculateTotalWidth = (): number => {
    return 110 + 130 + 110 + 130 + 160 + 90 + 90 + 90 + 110 + 90 + 90 + 164; // 1364px (操作列保持164px)
};

// 表格列配置
export const createTableColumns = (
    handleView: (row: EvaluationTableRow) => void,
    handleComplete: (row: EvaluationTableRow) => void,
    handleDelete: (row: EvaluationTableRow) => void,
    shouldFixActions: boolean = true
): DataTableColumns<EvaluationTableRow> => [
    {
        title: '状态',
        key: 'status',
        minWidth: 100,
        width: 110,
        render(row) {
            const status = row.status as EvaluationStatus;
            const statusColors: Record<EvaluationStatus, 'default' | 'error' | 'primary' | 'success' | 'info' | 'warning'> = {
                'self': 'info',
                'colleague': 'warning',
                'manager': 'primary',
                'completed': 'success'
            };
            return h(NTag, {
                type: statusColors[status] || 'default',
                size: 'small'
            }, {
                default: () => getStatusLabel(status)
            });
        }
    },
    {
        title: '部门/项目',
        key: 'department',
        minWidth: 120,
        width: 130
    },
    {
        title: '姓名',
        key: 'name',
        minWidth: 100,
        width: 110
    },
    {
        title: '日期',
        key: 'reviewDate',
        minWidth: 120,
        width: 130,
        render(row) {
            if (!row.reviewDate) return '未设置';
            return new Date(row.reviewDate).toLocaleDateString('zh-CN');
        }
    },
    {
        title: '备注',
        key: 'comment',
        minWidth: 150,
        ellipsis: {
            tooltip: true
        }
    },
    // 暂时注释附件列
    // {
    //     title: '附件',
    //     key: 'attachmentCount',
    //     minWidth: 70,
    //     width: 80,
    //     render(row) {
    //         const count = fyfcOssService.getAttachmentCount(row);
    //         if (count === 0) {
    //             return h('span', { style: 'color: #999' }, '无');
    //         }
    //         return h(NTag, {
    //             type: 'info',
    //             size: 'small'
    //         }, {
    //             default: () => `📎 ${count}`
    //         });
    //     }
    // },
    {
        title: '个人',
        key: 'employeeScore',
        minWidth: 80,
        width: 90,
        render(row) {
            return row.employeeScore?.toFixed(1) || '0.0';
        }
    },
    {
        title: '同事',
        key: 'colleagueScore',
        minWidth: 80,
        width: 90,
        render(row) {
            return row.colleagueScore?.toFixed(1) || '0.0';
        }
    },
    {
        title: '上级',
        key: 'managerScore',
        minWidth: 80,
        width: 90,
        render(row) {
            return row.managerScore?.toFixed(1) || '0.0';
        }
    },
    {
        title: '上级姓名',
        key: 'managerEvaluator',
        minWidth: 100,
        width: 110,
        render(row) {
            return row.managerEvaluator || '未评分';
        }
    },
    {
        title: '转发',
        key: 'additionalScore',
        minWidth: 80,
        width: 90,
        render(row) {
            return row.additionalScore?.toFixed(1) || '0.0';
        }
    },
    {
        title: '合计',
        key: 'totalScore',
        minWidth: 80,
        width: 90,
        render(row) {
            return row.totalScore?.toFixed(1) || '0.0';
        }
    },
    {
        title: '最终得分',
        key: 'calculatedScore',
        minWidth: 80,
        width: 90,
        render(row) {
            return row.calculatedScore?.toFixed(1) || '0.0';
        }
    },
    {
        title: '操作',
        key: 'actions',
        minWidth: 82,
        width: 164,
        ...(shouldFixActions ? { fixed: 'right' as const } : {}),
        render(row) {
            const isCompleted = row.status === 'completed';

            return h(NSpace, { size: 4 }, {
                default: () => [
                    h(NButton, {
                        size: 'small',
                        type: isCompleted ? 'info' : 'warning',
                        onClick: () => handleView(row)
                    }, {
                        default: () => isCompleted ? '查看' : '编辑',
                        icon: () => h(NIcon, null, { default: () => h(isCompleted ? ViewIcon : EditIcon) })
                    }),
                    !isCompleted && h(NButton, {
                        size: 'small',
                        type: 'success',
                        onClick: () => handleComplete(row)
                    }, {
                        default: () => '完成',
                        icon: () => h(NIcon, null, { default: () => h(CompleteIcon) })
                    }),
                    h(NButton, {
                        size: 'small',
                        type: 'error',
                        onClick: () => handleDelete(row)
                    }, {
                        default: () => '删除',
                        icon: () => h(NIcon, null, { default: () => h(DeleteIcon) })
                    })
                ].filter(Boolean)
            });
        }
    }
];
