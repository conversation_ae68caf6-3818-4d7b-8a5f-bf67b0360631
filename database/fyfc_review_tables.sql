-- =====================================================
-- 绩效评价系统数据库表结构
-- 基于 src/fconfig/fyfc/review/index.ts 类型定义
-- =====================================================

-- 设置字符集和排序规则
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 设置时区为北京时间 (可根据实际需要调整)
SET time_zone = '+08:00';

-- =====================================================
-- 1. 评价主表 (Evaluation)
-- =====================================================
DROP TABLE IF EXISTS `fyfc_evaluations`;
CREATE TABLE `fyfc_evaluations` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `department` varchar(100) DEFAULT NULL COMMENT '部门',
  `name` varchar(50) DEFAULT NULL COMMENT '被评价人姓名',
  `review_date` date DEFAULT NULL COMMENT '评价日期',
  `colleague_name` varchar(50) DEFAULT NULL COMMENT '被邀同事姓名',
  `manager_name` varchar(50) DEFAULT NULL COMMENT '主管上级姓名',
  `additional_score` decimal(5,2) DEFAULT NULL COMMENT '线上转发得分(0-10分)',
  `score` decimal(6,2) DEFAULT NULL COMMENT '最终得分',
  `comment` text DEFAULT NULL COMMENT '说明备注',
  `status` enum('self','colleague','manager','completed') DEFAULT 'self' COMMENT '评价状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `created_by` varchar(50) NOT NULL COMMENT '创建人',
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_department` (`department`),
  KEY `idx_status` (`status`),
  KEY `idx_colleague_name` (`colleague_name`),
  KEY `idx_manager_name` (`manager_name`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_review_date` (`review_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='绩效评价主表';

-- =====================================================
-- 2. 评价得分表 (EvaluationScore)
-- =====================================================
DROP TABLE IF EXISTS `fyfc_evaluation_scores`;
CREATE TABLE `fyfc_evaluation_scores` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `evaluation_id` int(11) NOT NULL COMMENT '评价主表ID',
  `evaluator` varchar(50) DEFAULT NULL COMMENT '评价人姓名',
  `type` enum('unknown','employee','colleague','manager','admin') NOT NULL COMMENT '评价人角色类型',
  `performance_score` decimal(5,2) DEFAULT NULL COMMENT '工作业绩得分(0-60分)',
  `attitude_score` decimal(4,2) DEFAULT NULL COMMENT '工作态度得分(0-10分)',
  `ability_score` decimal(4,2) DEFAULT NULL COMMENT '工作能力得分(0-10分)',
  `growth_score` decimal(4,2) DEFAULT NULL COMMENT '个人成长得分(0-10分)',
  `score` decimal(6,2) DEFAULT NULL COMMENT '小计得分',
  `signature` varchar(100) DEFAULT NULL COMMENT '评价人签名',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_evaluation_id` (`evaluation_id`),
  KEY `idx_evaluator` (`evaluator`),
  KEY `idx_type` (`type`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_evaluation_scores_evaluation_id` FOREIGN KEY (`evaluation_id`) REFERENCES `fyfc_evaluations` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评价得分表';

-- =====================================================
-- 3. 评价状态历史表 (EvaluationStatusHistory)
-- =====================================================
DROP TABLE IF EXISTS `fyfc_evaluation_status_history`;
CREATE TABLE `fyfc_evaluation_status_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `evaluation_id` int(11) NOT NULL COMMENT '评价主表ID',
  `previous_status` enum('self','colleague','manager','completed') DEFAULT NULL COMMENT '之前状态',
  `new_status` enum('self','colleague','manager','completed') NOT NULL COMMENT '新状态',
  `changed_by` varchar(50) NOT NULL COMMENT '状态变更人',
  `changed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '变更时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '变更备注',
  PRIMARY KEY (`id`),
  KEY `idx_evaluation_id` (`evaluation_id`),
  KEY `idx_changed_by` (`changed_by`),
  KEY `idx_changed_at` (`changed_at`),
  KEY `idx_new_status` (`new_status`),
  CONSTRAINT `fk_status_history_evaluation_id` FOREIGN KEY (`evaluation_id`) REFERENCES `fyfc_evaluations` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评价状态变更历史表';

-- =====================================================
-- 4. 创建视图 - 评价汇总视图
-- =====================================================
DROP VIEW IF EXISTS `v_evaluation_summary`;
CREATE VIEW `v_evaluation_summary` AS
SELECT
    e.id,
    e.department,
    e.name,
    e.review_date,
    e.colleague_name,
    e.manager_name,
    e.additional_score,
    e.score as final_score,
    e.comment,
    e.status,
    e.created_at,
    e.created_by,
    e.updated_at,
    e.updated_by,
    -- 员工自评得分
    (SELECT es.score FROM fyfc_evaluation_scores es WHERE es.evaluation_id = e.id AND es.type = 'employee' LIMIT 1) as employee_score,
    -- 同事评价得分
    (SELECT es.score FROM fyfc_evaluation_scores es WHERE es.evaluation_id = e.id AND es.type = 'colleague' LIMIT 1) as colleague_score,
    -- 主管评价得分
    (SELECT es.score FROM fyfc_evaluation_scores es WHERE es.evaluation_id = e.id AND es.type = 'manager' LIMIT 1) as manager_score,
    -- 评价人数统计
    (SELECT COUNT(*) FROM fyfc_evaluation_scores es WHERE es.evaluation_id = e.id) as evaluator_count
FROM fyfc_evaluations e;

-- =====================================================
-- 5. 插入示例数据
-- =====================================================

-- 示例评价记录
INSERT INTO `fyfc_evaluations` (`department`, `name`, `review_date`, `colleague_name`, `manager_name`, `additional_score`, `score`, `comment`, `status`, `created_at`, `created_by`) VALUES
('技术部', '张三', '2024-01-15', '李四', '王经理', 5.0, 83.5, '表现良好，技术能力突出', 'completed', '2024-01-15 10:00:00', '张三'),
('销售部', '李四', '2024-01-16', '王五', '赵总监', 3.0, 76.1, '销售业绩有待提升', 'manager', '2024-01-16 14:30:00', '李四'),
('技术部', '王五', '2024-01-17', '张三', '王经理', NULL, NULL, NULL, 'colleague', '2024-01-17 09:15:00', '王五');

-- 示例评价得分记录
INSERT INTO `fyfc_evaluation_scores` (`evaluation_id`, `evaluator`, `type`, `performance_score`, `attitude_score`, `ability_score`, `growth_score`, `score`, `signature`, `created_at`) VALUES
-- 张三的评价得分
(1, '张三', 'employee', 50.0, 8.0, 8.5, 8.0, 74.5, '张三', '2024-01-15 10:30:00'),
(1, '李四', 'colleague', 52.0, 8.5, 9.0, 8.5, 78.0, '李四', '2024-01-15 15:20:00'),
(1, '王经理', 'manager', 55.0, 9.0, 9.0, 9.0, 82.0, '王经理', '2024-01-15 17:45:00'),

-- 李四的评价得分
(2, '李四', 'employee', 45.0, 7.5, 7.0, 7.5, 67.0, '李四', '2024-01-16 14:45:00'),
(2, '王五', 'colleague', 48.0, 8.0, 7.5, 8.0, 71.5, '王五', '2024-01-16 16:30:00'),

-- 王五的评价得分
(3, '王五', 'employee', 48.0, 8.0, 8.0, 7.5, 71.5, '王五', '2024-01-17 09:30:00'),
(3, '张三', 'colleague', 50.0, 8.5, 8.0, 8.0, 74.5, '张三', '2024-01-17 11:15:00');

-- 示例状态变更历史
INSERT INTO `fyfc_evaluation_status_history` (`evaluation_id`, `previous_status`, `new_status`, `changed_by`, `changed_at`, `remark`) VALUES
(1, NULL, 'self', '张三', '2024-01-15 10:00:00', '创建评价记录'),
(1, 'self', 'colleague', '张三', '2024-01-15 10:30:00', '完成自评'),
(1, 'colleague', 'manager', '李四', '2024-01-15 15:20:00', '完成同事评价'),
(1, 'manager', 'completed', '王经理', '2024-01-15 17:45:00', '完成主管评价'),

(2, NULL, 'self', '李四', '2024-01-16 14:30:00', '创建评价记录'),
(2, 'self', 'colleague', '李四', '2024-01-16 14:45:00', '完成自评'),
(2, 'colleague', 'manager', '王五', '2024-01-16 16:30:00', '完成同事评价'),

(3, NULL, 'self', '王五', '2024-01-17 09:15:00', '创建评价记录'),
(3, 'self', 'colleague', '王五', '2024-01-17 09:30:00', '完成自评');

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 6. 常用查询示例
-- =====================================================

-- 查询所有评价汇总信息
-- SELECT * FROM v_evaluation_summary ORDER BY created_at DESC;

-- 查询特定用户的待办评价
-- SELECT * FROM fyfc_evaluations WHERE manager_name = '王经理' AND status IN ('self', 'colleague');

-- 查询评价状态变更历史
-- SELECT h.*, e.name, e.department FROM fyfc_evaluation_status_history h
-- LEFT JOIN fyfc_evaluations e ON h.evaluation_id = e.id
-- ORDER BY h.changed_at DESC;

-- 查询评价得分详情
-- SELECT e.name, e.department, es.type, es.performance_score, es.attitude_score, es.ability_score, es.growth_score, es.score
-- FROM fyfc_evaluations e
-- LEFT JOIN fyfc_evaluation_scores es ON e.id = es.evaluation_id
-- WHERE e.id = 1;

-- =====================================================
-- 7. TIMESTAMP时间格式说明和查询示例
-- =====================================================

-- TIMESTAMP vs DATETIME 说明:
-- 1. TIMESTAMP 自动处理时区转换，存储为UTC，查询时转换为当前时区
-- 2. TIMESTAMP 占用4字节，DATETIME占用8字节，节省存储空间
-- 3. TIMESTAMP 范围 1970-2038，对绩效评价系统完全够用
-- 4. TIMESTAMP 更适合多时区应用和自动时间戳管理

-- 时区相关操作示例:
-- 查看当前时区: SELECT @@time_zone;
-- 设置会话时区: SET time_zone = '+08:00';  -- 北京时间
-- 设置会话时区: SET time_zone = '+00:00';  -- UTC时间

-- 查询今天创建的评价
-- SELECT * FROM fyfc_evaluations WHERE DATE(created_at) = CURDATE();

-- 查询本周的评价
-- SELECT * FROM fyfc_evaluations WHERE YEARWEEK(created_at) = YEARWEEK(NOW());

-- 查询本月的评价
-- SELECT * FROM fyfc_evaluations WHERE YEAR(created_at) = YEAR(NOW()) AND MONTH(created_at) = MONTH(NOW());

-- 查询指定日期范围的评价
-- SELECT * FROM fyfc_evaluations WHERE review_date BETWEEN '2024-01-01' AND '2024-01-31';

-- 查询最近7天的评价活动
-- SELECT * FROM fyfc_evaluations WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY);

-- 按月份统计评价数量
-- SELECT YEAR(created_at) as year, MONTH(created_at) as month, COUNT(*) as count
-- FROM fyfc_evaluations
-- GROUP BY YEAR(created_at), MONTH(created_at)
-- ORDER BY year DESC, month DESC;

-- 查询评价处理时长（从创建到完成的时间）
-- SELECT e.name, e.department, e.created_at,
--        (SELECT MAX(h.changed_at) FROM fyfc_evaluation_status_history h WHERE h.evaluation_id = e.id AND h.new_status = 'completed') as completed_at,
--        TIMESTAMPDIFF(HOUR, e.created_at, (SELECT MAX(h.changed_at) FROM fyfc_evaluation_status_history h WHERE h.evaluation_id = e.id AND h.new_status = 'completed')) as processing_hours
-- FROM fyfc_evaluations e
-- WHERE e.status = 'completed';

-- 查询超过指定天数未完成的评价
-- SELECT * FROM fyfc_evaluations
-- WHERE status != 'completed'
-- AND DATEDIFF(NOW(), created_at) > 7;

-- 按部门统计评价完成情况
-- SELECT department,
--        COUNT(*) as total_evaluations,
--        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_evaluations,
--        ROUND(SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as completion_rate
-- FROM fyfc_evaluations
-- GROUP BY department;
