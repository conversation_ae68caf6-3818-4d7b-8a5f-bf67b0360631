import axios, { AxiosResponse } from 'axios';
import { stringify } from 'qs';

// API 响应基础类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  success: boolean;
  timestamp: number;
}

// 分页响应类型
export interface PaginatedResponse<T = any> {
  data: T[];
  page: number;
  size: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrevious: boolean;
  isFirst: boolean;
  isLast: boolean;
}

// 附件相关类型定义
export interface FyfcAttachment {
  id: string;
  fileName: string;
  fileKey: string;
  bucketName?: string;
  fileSize: number;
  fileType: string;
  uploadTime: number;
  uploadBy: string;
  fileUrl?: string;
}

// 评价相关类型定义
export interface EvaluationDto {
  id?: number;
  department: string;
  name: string;
  reviewDate: number; // 时间戳
  colleagueName?: string;
  managerName?: string;
  score?: number;
  calculateScore?: number;
  additionalScore?: number;
  status: string;
  comment?: string;
  attachments?: string; // 新增：附件JSON字符串
  createdBy?: string;
  createdAt?: number;
  updatedBy?: string;
  updatedAt?: number;
}

export interface EvaluationQueryDto {
  page: number;
  size: number;
  department?: string;
  name?: string;
  status?: string;
  colleagueName?: string;
  managerName?: string;
  currentUser?: string;
  userName?: string;
  queryType?: string;
  sortBy?: string;
  sortDirection?: string;
  startDate?: number;
  endDate?: number;
}

export interface EvaluationFormDto {
  department: string;
  name: string;
  reviewDate: number;
  colleagueName?: string;
  managerName?: string;
  calculateScore?: number;
  additionalScore?: number;
  comment?: string;
  attachments?: string; // 新增：附件JSON字符串
}

export interface EvaluationUpdateDto {
  id: number;
  department?: string;
  name?: string;
  reviewDate?: number;
  colleagueName?: string;
  managerName?: string;
  additionalScore?: number;
  score?: number;
  calculateScore?: number;
  comment?: string;
  attachments?: string; // 新增：附件JSON字符串
  updateReason?: string;
  sendNotification?: boolean;
}

export interface ScoreFormDto {
  evaluationId: number;
  type?: string; // 评分类型：employee、colleague、manager
  performanceScore: number;
  attitudeScore: number;
  abilityScore: number;
  growthScore: number;
  evaluationScore: number; // 评价总分（用于更新evaluation表的score字段）
  signature?: string;
}

export interface EvaluationStatsDto {
  totalEvaluations: number;
  completedEvaluations: number;
  inProgressEvaluations: number;
  completionRate: number;
  averageScore?: number;
  maxScore?: number;
  minScore?: number;
}

// API 基础配置
const API_BASE_URL = '/fyschedule/api/fyfc/evaluation';

// 通用请求方法
async function request<T>(
  method: 'GET' | 'POST' | 'PUT' | 'DELETE',
  url: string,
  data?: any,
  params?: any
): Promise<ApiResponse<T>> {
  try {
    const config = {
      method,
      url: `${API_BASE_URL}${url}`,
      headers: {
        'Content-Type': 'application/json',
      },
      withCredentials: true,
      ...(data ? { data } : {}),
      ...(params ? { params, paramsSerializer: (params: any) => stringify(params) } : {})
    };

    const response: AxiosResponse<ApiResponse<T>> = await axios(config);
    return response.data;
  } catch (error: any) {
    console.error(`API请求失败 [${method} ${url}]:`, error);

    // 处理错误响应
    if (error.response?.data) {
      return error.response.data;
    }

    // 默认错误响应
    return {
      code: 500,
      message: error.message || '网络请求失败',
      data: null as T,
      success: false,
      timestamp: Date.now()
    };
  }
}

// 附件工具函数
export const attachmentUtils = {
  // 从评价中提取附件列表
  getAttachmentsFromEvaluation(evaluation: EvaluationDto): FyfcAttachment[] {
    if (!evaluation.attachments) {
      return [];
    }

    try {
      return JSON.parse(evaluation.attachments) as FyfcAttachment[];
    } catch (error) {
      console.error('解析附件数据失败:', error);
      return [];
    }
  },

  // 统计附件数量
  getAttachmentCount(evaluation: EvaluationDto): number {
    return this.getAttachmentsFromEvaluation(evaluation).length;
  },

  // 格式化文件大小
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  // 获取文件类型图标
  getFileTypeIcon(fileType: string): string {
    if (fileType.includes('image')) return '🖼️';
    if (fileType.includes('pdf')) return '📄';
    if (fileType.includes('word')) return '📝';
    if (fileType.includes('excel') || fileType.includes('spreadsheet')) return '📊';
    if (fileType.includes('powerpoint') || fileType.includes('presentation')) return '📋';
    return '📎';
  }
};

// 管理员 API
export const adminApi = {
  // 分页查询评价数据
  async searchEvaluations(queryDto: EvaluationQueryDto): Promise<ApiResponse<PaginatedResponse<EvaluationDto>>> {
    return request('POST', '/admin/search', queryDto);
  },

  // 更新评价状态
  async updateEvaluationStatus(id: number, status: string, operator: string): Promise<ApiResponse<boolean>> {
    return request('PUT', `/admin/status/${id}`, null, { status, operator });
  },

  // 获取统计数据
  async getEvaluationStats(): Promise<ApiResponse<EvaluationStatsDto>> {
    return request('GET', '/admin/stats');
  },

  // 导出评价数据
  async exportEvaluations(queryDto: EvaluationQueryDto): Promise<ApiResponse<EvaluationDto[]>> {
    return request('POST', '/admin/export', queryDto);
  },

  // 批量删除评价
  async batchDeleteEvaluations(evaluationIds: number[], operator: string): Promise<ApiResponse<number>> {
    return request('DELETE', '/admin/batch', evaluationIds, { operator });
  },

  // 删除单个评价（使用批量删除API）
  async deleteEvaluation(id: number, operator: string): Promise<ApiResponse<number>> {
    return this.batchDeleteEvaluations([id], operator);
  },

  // 重置评价状态
  async resetEvaluationStatus(id: number, operator: string): Promise<ApiResponse<boolean>> {
    return request('PUT', `/admin/reset/${id}`, null, { operator });
  },

  // 获取系统配置选项
  async getSystemOptions(): Promise<ApiResponse<any>> {
    return request('GET', '/admin/options');
  },

  // 获取评价详情（管理员专用，包含完整评分数据）
  async getEvaluationDetail(id: number, operator: string): Promise<ApiResponse<any>> {
    return request('GET', `/admin/${id}`, null, { operator });
  }
};

// 员工 API
export const staffApi = {
  // 查询评价历史
  async getEvaluationHistory(userName: string, queryDto: EvaluationQueryDto): Promise<ApiResponse<PaginatedResponse<EvaluationDto>>> {
    // userName作为查询参数，queryDto作为请求体
    return request('POST', '/staff/history', queryDto, { userName });
  },

  // 查询待办评价
  async getPendingEvaluations(userName: string): Promise<ApiResponse<EvaluationDto[]>> {
    return request('GET', '/staff/pending', null, { userName });
  },

  // 获取员工统计
  async getEvaluationStats(userName: string): Promise<ApiResponse<EvaluationStatsDto>> {
    return request('GET', '/staff/stats', null, { userName });
  },

  // 创建评价
  async createEvaluation(formDto: EvaluationFormDto, creator: string): Promise<ApiResponse<EvaluationDto>> {
    return request('POST', '/staff/create', formDto, { creator });
  },

  // 更新评价
  async updateEvaluation(updateDto: EvaluationUpdateDto, updater: string): Promise<ApiResponse<boolean>> {
    return request('PUT', '/staff/update', updateDto, { updater });
  },

  // 获取评价详情
  async getEvaluationDetail(id: number, userName: string): Promise<ApiResponse<any>> {
    return request('GET', `/staff/${id}`, null, { userName });
  },

  // 提交自评分数
  async submitSelfScore(scoreForm: ScoreFormDto, evaluator: string): Promise<ApiResponse<boolean>> {
    return request('POST', '/staff/score/self', scoreForm, { evaluator });
  },

  // 提交同事评分
  async submitColleagueScore(scoreForm: ScoreFormDto, evaluator: string): Promise<ApiResponse<boolean>> {
    return request('POST', '/staff/score/colleague', scoreForm, { evaluator });
  },

  // 删除评价
  async deleteEvaluation(id: number, operator: string): Promise<ApiResponse<boolean>> {
    return request('DELETE', `/staff/${id}`, null, { operator });
  },

  // 检查用户权限
  async checkUserPermission(id: number, userName: string): Promise<ApiResponse<boolean>> {
    return request('GET', `/staff/${id}/permission`, null, { userName });
  },

  // 获取表单初始数据
  async getFormInitData(userName: string): Promise<ApiResponse<any>> {
    return request('GET', '/staff/form/init', null, { userName });
  }
};

// 主管 API
export const managerApi = {
  // 查询待评价记录
  async getPendingEvaluations(managerName: string, queryDto: EvaluationQueryDto): Promise<ApiResponse<PaginatedResponse<EvaluationDto>>> {
    return request('POST', '/manager/pending', queryDto, { managerName });
  },

  // 查询已完成评价
  async getCompletedEvaluations(managerName: string, queryDto: EvaluationQueryDto): Promise<ApiResponse<PaginatedResponse<EvaluationDto>>> {
    return request('POST', '/manager/completed', queryDto, { managerName });
  },

  // 提交主管评分
  async submitManagerScore(scoreForm: ScoreFormDto, managerName: string): Promise<ApiResponse<boolean>> {
    return request('POST', '/manager/score', scoreForm, { managerName });
  },

  // 批量审核评价
  async batchApproveEvaluations(evaluationIds: number[], managerName: string): Promise<ApiResponse<number>> {
    return request('POST', '/manager/batch-approve', evaluationIds, { managerName });
  },

  // 获取主管统计
  async getEvaluationStats(managerName: string): Promise<ApiResponse<EvaluationStatsDto>> {
    return request('GET', '/manager/stats', null, { managerName });
  },

  // 获取负责的员工列表
  async getManagedEmployees(managerName: string): Promise<ApiResponse<string[]>> {
    return request('GET', '/manager/employees', null, { managerName });
  },

  // 查看评价详情
  async getEvaluationDetail(id: number, managerName: string): Promise<ApiResponse<any>> {
    return request('GET', `/manager/${id}`, null, { managerName });
  },

  // 驳回评价
  async rejectEvaluation(id: number, managerName: string, reason?: string): Promise<ApiResponse<boolean>> {
    return request('POST', `/manager/${id}/reject`, null, { managerName, reason });
  },

  // 导出下属评价数据
  async exportSubordinateEvaluations(managerName: string, queryDto: EvaluationQueryDto): Promise<ApiResponse<EvaluationDto[]>> {
    return request('POST', '/manager/export', queryDto, { managerName });
  }
};

// 通用 API
export const commonApi = {
  // 获取评价基本信息
  async getEvaluationById(id: number): Promise<ApiResponse<EvaluationDto>> {
    return request('GET', `/common/${id}`);
  },

  // 获取部门列表
  async getDepartmentList(): Promise<ApiResponse<string[]>> {
    return request('GET', '/common/departments');
  },

  // 获取状态选项
  async getStatusOptions(): Promise<ApiResponse<any[]>> {
    return request('GET', '/common/status-options');
  },

  // 获取角色选项
  async getUserRoleOptions(): Promise<ApiResponse<any[]>> {
    return request('GET', '/common/role-options');
  },

  // 验证评价数据
  async validateEvaluationData(id: number): Promise<ApiResponse<boolean>> {
    return request('POST', `/common/${id}/validate`);
  },

  // 计算总分
  async calculateTotalScore(id: number): Promise<ApiResponse<number>> {
    return request('GET', `/common/${id}/total-score`);
  },

  // 检查是否可编辑
  async canEditEvaluation(id: number, userName: string): Promise<ApiResponse<boolean>> {
    return request('GET', `/common/${id}/can-edit`, null, { userName });
  },

  // 发送通知
  async sendEvaluationNotification(id: number, notificationType: string): Promise<ApiResponse<boolean>> {
    return request('POST', `/common/${id}/notification`, null, { notificationType });
  }
};

// 导出所有 API
export const fyfcReviewApi = {
  admin: adminApi,
  staff: staffApi,
  manager: managerApi,
  common: commonApi
};

export default fyfcReviewApi;
